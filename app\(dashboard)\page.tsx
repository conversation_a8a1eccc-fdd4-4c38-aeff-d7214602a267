"use client";

import { useEffect } from "react";
import {
  Package,
  Users,
  DollarSign
} from "lucide-react";

import { useStateManager } from "@/hooks/use-context";

import { TokenDisplay } from "@/features/dashboard/components/token-display";
import { SalesDisplay } from "@/features/dashboard/components/sales-display";
import { StatsCard } from "@/features/dashboard/components/stats-card";
import { QuickActions } from "@/features/dashboard/components/quick-actions";
import { MobileFAB } from "@/components/mobile-fab";

// Mock organization data
const mockOrganization = {
  id: 1,
  name: "sample",
  phone: "0626370989",
  address: "Dar es salaam",
  description: "this is sample",
  featured_image: "/storage/",
  deleted_at: null,
  created_at: "2025-07-29T20:39:46.000000Z",
  updated_at: "2025-07-29T20:39:46.000000Z",
  owner_id: 1,
  team_id: 1,
  country: null,
  region: null,
  district: null,
  ward: null,
  street: null,
  latitude: null,
  longitude: null,
  tokens: 0,
  users: [
    {
      id: 1,
      name: "<PERSON>",
      email: "mark<PERSON><PERSON><PERSON>@gmail.com",
      email_verified_at: "2025-07-28T07:55:24.000000Z",
      created_at: "2025-07-28T07:55:24.000000Z",
      updated_at: "2025-07-28T07:55:24.000000Z",
      deleted_at: null,
      active_team_id: null,
      phone: "+255654303353",
      phone_verified_at: null,
      needs_password_change: true,
      apple_id: null,
      google_id: null,
      facebook_id: null,
      pivot: {
        organization_id: 1,
        user_id: 1
      }
    }
  ],
  owner: {
    id: 1,
    name: "Mark Mayalla",
    email: "<EMAIL>",
    email_verified_at: "2025-07-28T07:55:24.000000Z",
    created_at: "2025-07-28T07:55:24.000000Z",
    updated_at: "2025-07-28T07:55:24.000000Z",
    deleted_at: null,
    active_team_id: null,
    phone: "+255654303353",
    phone_verified_at: null,
    needs_password_change: true,
    apple_id: null,
    google_id: null,
    facebook_id: null
  },
  domains: []
};

// Mock dashboard stats
const mockStats = {
  todaySales: 12.89,
  totalSales: 45678.90,
  totalCustomers: 156,
  totalProducts: 89,
  totalOrders: 234,
  lowStockItems: 5,
  recentSales: [],
  topProducts: [],
  salesTrend: []
};

const OrganizationDashboard = () => {
  const organizationIdString = localStorage.getItem("current_organization");
  const organizationId = organizationIdString ? Number(organizationIdString) : undefined;

  return (
    <div className="min-h-screen bg-gray-50/50">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Header Section */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600 mt-1">Welcome back! Here's what's happening with your business today.</p>
          </div>
          <div className="flex items-center gap-3">
            <div className="text-sm text-gray-500">
              Last updated: {new Date().toLocaleTimeString()}
            </div>
          </div>
        </div>

        {/* Featured Sales & Tokens Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <SalesDisplay
              todaySales={mockStats.todaySales}
              totalSales={mockStats.totalSales}
              trend={{
                value: 15.2,
                isPositive: true
              }}
            />
          </div>
          <div className="lg:col-span-1">
            <TokenDisplay
              tokens={mockOrganization.tokens}
              onAddTokens={() => {
                console.log("Add tokens clicked");
              }}
            />
          </div>
        </div>


        {/* Business Metrics Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          <StatsCard
            title="Monthly Revenue"
            value={`TSH ${mockStats.totalSales.toLocaleString()}/=`}
            icon={DollarSign}
            description="This month's total earnings"
            className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-100 hover:border-green-200 hover:shadow-lg transition-all duration-300"
            valueClassName="text-green-700 text-2xl lg:text-3xl"
          />
          <StatsCard
            title="Total Users"
            value={mockStats.totalCustomers}
            icon={Users}
            description="Registered team members"
            className="bg-white border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all duration-300"
            valueClassName="text-blue-600"
          />

          <StatsCard
            title="Products in Stock"
            value={mockStats.totalProducts}
            icon={Package}
            description="Available inventory"
            className="bg-white border border-gray-200 hover:border-purple-300 hover:shadow-md transition-all duration-300"
            valueClassName="text-purple-600"
          />
        </div>

        {/* Quick Actions Section */}
        <div className="bg-white rounded-xl border border-gray-200 p-6 hidden sm:block">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">Quick Actions</h2>
            <div className="text-sm text-gray-500">Get things done faster</div>
          </div>
          {organizationId !== undefined && (
            <QuickActions organizationId={organizationId} />
          )}
        </div>

        {/* Mobile Floating Action Button */}
        <MobileFAB />
      </div>
    </div>
  );
};

export default OrganizationDashboard;
