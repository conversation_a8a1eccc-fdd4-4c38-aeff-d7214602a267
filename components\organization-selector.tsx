"use client";

import { useState } from "react";
import { ChevronDown, Building2, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useRouter } from "next/navigation";

interface Organization {
  id: string;
  name: string;
  description?: string;
}

interface OrganizationSelectorProps {
  currentOrganization?: Organization;
  organizations?: Organization[];
}

export const OrganizationSelector = ({ 
  currentOrganization,
  organizations = []
}: OrganizationSelectorProps) => {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);

  const handleOrganizationSelect = (orgId: string) => {
    localStorage.setItem("current_organization", orgId);
    router.push("/");
    setIsOpen(false);
  };

  const handleCreateNew = () => {
    router.push("/setup/create-organization");
    setIsOpen(false);
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="flex items-center gap-2 px-3 py-2 h-auto hover:bg-gray-100 transition-colors"
        >
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 bg-primary/10 rounded-md flex items-center justify-center">
              <Building2 className="w-4 h-4 text-primary" />
            </div>
            <span className="font-medium text-gray-900 max-w-[150px] truncate">
              {currentOrganization?.name || "Select Organization"}
            </span>
          </div>
          <ChevronDown className="w-4 h-4 text-gray-500" />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align="start" className="w-64" sideOffset={8}>
        <div className="px-2 py-1.5">
          <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">
            Organizations
          </p>
        </div>
        
        <DropdownMenuSeparator />

        {organizations.length > 0 ? (
          <>
            {organizations && organizations.map((org) => (
              <DropdownMenuItem
                key={org.id}
                onClick={() => handleOrganizationSelect(org.id)}
                className="flex items-center gap-3 px-3 py-2 cursor-pointer"
              >
                <div className="w-8 h-8 bg-primary/10 rounded-md flex items-center justify-center flex-shrink-0">
                  <Building2 className="w-4 h-4 text-primary" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="font-medium text-gray-900 truncate">
                    {org.name}
                  </p>
                  {org.description && (
                    <p className="text-xs text-gray-500 truncate">
                      {org.description}
                    </p>
                  )}
                </div>
                {currentOrganization?.id === org.id && (
                  <div className="w-2 h-2 bg-primary rounded-full" />
                )}
              </DropdownMenuItem>
            ))}
            
            <DropdownMenuSeparator />
          </>
        ) : (
          <>
            <div className="px-3 py-6 text-center">
              <Building2 className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">No organizations found</p>
              <p className="text-xs text-gray-400 mt-1">Create your first organization to get started</p>
            </div>
            <DropdownMenuSeparator />
          </>
        )}

        <DropdownMenuItem
          onClick={handleCreateNew}
          className="flex items-center gap-3 px-3 py-2 cursor-pointer text-primary hover:text-primary"
        >
          <div className="w-8 h-8 bg-primary/10 rounded-md flex items-center justify-center">
            <Plus className="w-4 h-4 text-primary" />
          </div>
          <span className="font-medium">Create New Organization</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
