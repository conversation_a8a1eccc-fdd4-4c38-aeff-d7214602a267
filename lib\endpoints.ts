
export default class IziApi {

    public static api = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000/api";

    // Auth endpoints
    public static login = `${this.api}/auth/login`;
    public static register = `${this.api}/auth/register`;
    public static loggedInUser = `${this.api}/auth/user`;

    // organizations endpoints
    public static createOrganization = `${this.api}/organizations/store`;
    public static getOrganizations = `${this.api}/config/organizations`;
    public static singleOrganization = (id: number) => `${this.api}/config/organizations/${id}`;

}