"use client"

import { But<PERSON> } from "@/components/ui/button";
import { useStateManager } from "@/hooks/use-context";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import React from "react";

interface AuthLayoutProps {
    children: React.ReactNode;
}

const AuthLayout = ({ children }: AuthLayoutProps) => {
    const { isAuthenticated, isLoading } = useStateManager();

    const pathname = usePathname();

    //  const router = useRouter();

    // React.useEffect(() => {
    //     if (isAuthenticated) {
    //         router.replace("/");
    //     }
    // }, [isAuthenticated, router]);

    if (isLoading) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                    <p className="text-muted-foreground">Loading...</p>
                </div>
            </div>
        );
    }   
    
    return (
        <main className="bg-gradient-to-br from-secondary/20 via-white to-primary/5 min-h-screen">
            <div className="mx-auto max-w-screen-xl px-6 lg:px-8">
                <nav className="flex justify-between items-center py-6">
                    <Image 
                        src="/logo.png" 
                        alt="Izi sales logo" 
                        width={120} 
                        height={36}
                        className="h-10 w-auto"
                    />
                    <Button 
                        variant="outline" 
                        className="border-primary/20 hover:bg-primary hover:text-primary-foreground transition-all duration-300"
                        asChild
                    >
                        <Link href={pathname == "/sign-in" ? "/sign-up" : "/sign-in"}>
                            {pathname === "/sign-in" ? "Sign Up" : "Login"}
                        </Link>
                    </Button>
                </nav>
                
                <div className="flex items-center justify-center min-h-[calc(100vh-120px)] py-8">
                    <div className="w-full max-w-6xl mx-auto">
                        <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
                            {/* Features Section - Hidden on mobile */}
                            <div className="hidden lg:block space-y-8">
                                <div className="space-y-6">
                                    <h1 className="text-4xl xl:text-5xl font-bold text-primary leading-tight">
                                        Manage Your Shop <br />
                                        <span className="text-secondary-foreground">Digitally</span>
                                    </h1>
                                    <p className="text-muted-foreground text-lg xl:text-xl">
                                        Transform your business with our comprehensive digital shop management solution.
                                    </p>
                                </div>
                                
                                <div className="space-y-6">
                                    <div className="flex items-start space-x-4">
                                        <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center flex-shrink-0">
                                            <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h2M4 4h5.01M4 20h5.01" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 className="font-semibold text-primary mb-2">Scan and Sell</h3>
                                            <p className="text-muted-foreground">Sell products quickly and easily, even with a barcode scanner, for a smoother transaction experience.</p>
                                        </div>
                                    </div>
                                    
                                    <div className="flex items-start space-x-4">
                                        <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center flex-shrink-0">
                                            <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M9 9h.01M15 9h.01" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 className="font-semibold text-primary mb-2">Inventory Management</h3>
                                            <p className="text-muted-foreground">Track your stock in real-time, manage levels efficiently, and avoid stockouts or overstocking problems.</p>
                                        </div>
                                    </div>
                                    
                                    <div className="flex items-start space-x-4">
                                        <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center flex-shrink-0">
                                            <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 className="font-semibold text-primary mb-2">Multi-Device Support</h3>
                                            <p className="text-muted-foreground">Access and manage your business on mobile, tablets, or desktops, ensuring smooth operations anywhere.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            {/* Sign In Form */}
                            <div className="flex justify-center lg:justify-end">
                                {children}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    );
};

export default AuthLayout;
