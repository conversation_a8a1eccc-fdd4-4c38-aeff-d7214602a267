"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Coins, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface TokenDisplayProps {
  tokens: number;
  onAddTokens?: () => void;
}

export const TokenDisplay = ({ tokens, onAddTokens }: TokenDisplayProps) => {
  const formatTokens = (amount: number) => {
    return amount.toLocaleString();
  };

  return (
    <Card className="border-none shadow-lg bg-gradient-to-br from-yellow-50 to-amber-50 hover:shadow-xl transition-all duration-300 h-full">
      <CardContent className="p-6 h-full flex flex-col justify-center">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Coins className="h-6 w-6 text-amber-600" />
            <span className="text-sm font-medium text-muted-foreground">Available Tokens</span>
          </div>
          {onAddTokens && (
            <Button
              size="sm"
              variant="outline"
              onClick={onAddTokens}
              className="border-amber-300 text-amber-700 hover:bg-amber-100"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add
            </Button>
          )}
        </div>

        <div className="flex items-center gap-3">
          <div className="text-3xl font-bold text-amber-700">
            {formatTokens(tokens)} units
          </div>
          <Badge variant="secondary" className="bg-amber-100 text-amber-800">
            {tokens >= 100 ? "Active" : "Low Balance"}
          </Badge>
        </div>

        <div className="mt-2 text-xs text-muted-foreground">
          {tokens >= 12
            ? "Sufficient for operations"
            : "Consider adding more tokens"
          }
        </div>
      </CardContent>
    </Card>
  );
};
