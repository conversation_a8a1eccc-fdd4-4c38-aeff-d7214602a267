import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useStateManager } from "@/hooks/use-context";
import { CreateOrganizationFormData } from "../schemas";
import { useRouter } from "next/navigation";
import { Organization } from "../server/Organization";

export const useCreateOrganization = () => {
  const queryClient = useQueryClient();
  // const { user, setHasAnyOrganization } = useStateManager();
  const router = useRouter();

  const mutation = useMutation<any, Error, CreateOrganizationFormData>({ 
    mutationFn: async (data: CreateOrganizationFormData) => {
      const res = await Organization.create(data);
      return res;
    },
    onSuccess: (data) => {
      toast.success("Organization created successfully!");
      
      // Update the user's organization status
      // setHasAnyOrganization(true);
      
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ["organizations"] });
      
      // Redirect to choose organization or dashboard
      router.push("/setup/choose-organization");
    },
    onError: (error: any) => {
      console.error("Error creating organization:", error);
      const errorMessage = error?.response?.data?.message || error.message || "Failed to create organization";
      toast.error(errorMessage);
    },
  });

  return mutation;
};
