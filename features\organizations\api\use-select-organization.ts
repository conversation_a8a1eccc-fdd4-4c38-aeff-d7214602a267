import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Organization } from "../server/Organization";
import { useRouter } from "next/navigation";
import { useStateManager } from "@/hooks/use-context";

export const useSelectOrganization = () => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const { setCurrentOrganization } = useStateManager();

  const mutation = useMutation<any, Error, number>({ 
    mutationFn: async (organizationId: number) => {
      const res = await Organization.select(organizationId);
      return res;
    },
    onSuccess: (data) => {
      toast.success("Organization selected successfully!");

      // Set current organization in context
      setCurrentOrganization(data);

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ["organizations"] });
      queryClient.invalidateQueries({ queryKey: ["auth"] });

      // Redirect to organization-specific dashboard
      localStorage.setItem("current_organization", data.id);
      router.push(`/`);
    },
    onError: (error: any) => {
      console.error("Error selecting organization:", error);
      const errorMessage = error?.response?.data?.message || error.message || "Failed to select organization";
      toast.error(errorMessage);
    },
  });

  return mutation;
};
