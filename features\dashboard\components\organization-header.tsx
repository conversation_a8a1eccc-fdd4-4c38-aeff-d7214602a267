"use client";

import { Building2 } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface Organization {
  id: number;
  name: string;
  phone: string;
  address: string;
  description: string;
  featured_image: string;
  tokens: number;
  users: any[];
  owner: any;
}

interface OrganizationHeaderProps {
  organization: Organization;
}

export const OrganizationHeader = ({ organization }: OrganizationHeaderProps) => {
  return (
    <div className="flex items-center gap-4 bg-primary rounded-lg p-6 text-white">
      {/* Avatar */}
      <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center flex-shrink-0">
        <Building2 className="w-6 h-6 text-white" />
      </div>

      {/* Organization Info */}
      <div className="flex-1 min-w-0">
        <h1 className="text-2xl font-bold mb-1">{organization.name}</h1>
        {organization.description && (
          <p className="text-blue-100 text-sm">{organization.description}</p>
        )}
      </div>
    </div>
  );
};
