import Image from "next/image";
import Link from "next/link";
import { DottedSeparator } from "./dotted-separator";
import { Navigation } from "./navigation";

export const Sidebar = () => {
  return (
    <aside className="h-full bg-neutral-100 p-4 w-full shadow-md">
      <div className="flex justify-center">
        <Link href="/">
          <Image 
            src="/logo.png" 
            alt="IZI Sale Logo" 
            width={120} 
            height={36} 
            className="mb-4 hover:scale-105 transition-transform h-10 w-auto" 
          />
        </Link>
      </div>
      <DottedSeparator className="mb-4"/>
      <Navigation />
    </aside>
  );
};