"use client"

import React from 'react'
// import { useCurrentUser } from '../api/use-current-user';
import { Loader, LogOut } from 'lucide-react';
import { AvatarFallback } from '@radix-ui/react-avatar';
import { Avatar } from '@/components/ui/avatar';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { DottedSeparator } from '@/components/dotted-separator';
import { useStateManager } from '@/hooks/use-context';

export const UserButton = () => {
    const { user, isLoading , handleLogout } = useStateManager();

    if (isLoading) {
        return (
            <div className="size-10 rounded-full flex items-center justify-center bg-neutral-200 border border-neutral-300 ">
                <Loader className="size-4 animate-spin text-muted-foreground" />
            </div>
        )
    }

    if (!user) {
        return null;
    }


    const avatarFallback = user?.name ? user?.name.charAt(0).toUpperCase() : user?.email.charAt(0).toUpperCase() ?? "U";
    return (
        <DropdownMenu modal={false}>
            <DropdownMenuTrigger className="outline-none relative">
                <Avatar className="bg-neutral-200 hover:opacity-75 transition border border-neutral-300 font-medium text-neutral-500 flex items-center justify-center">
                    <AvatarFallback>
                        {avatarFallback}
                    </AvatarFallback>
                </Avatar>
            </DropdownMenuTrigger>

            <DropdownMenuContent align="end" side="bottom" className="w-60" sideOffset={10}>
                <div className="flex flex-col items-center justify-center px-2.5 py-4">
                <Avatar className="size-[52px] bg-neutral-200 hover:opacity-75 transition border border-neutral-300 font-medium text-neutral-500 flex items-center justify-center">
                    <AvatarFallback>
                        {avatarFallback}
                    </AvatarFallback>
                </Avatar>
                    <div className='flex flex-col items-center justify-center'>
                        <p className='text-sm font-medium text-neutral-900'>
                            {user.name || "Unknown"}
                        </p>
                        <p className='text-sm font-medium text-neutral-900'>
                            {user.phone || "Phone"}
                        </p>
                        <p className='text-sm font-medium text-neutral-900'>
                            {user.email || "Email"}
                        </p>
                    </div>

                    <DottedSeparator className="mt-3 mb-1" />

                    <DropdownMenuItem
                        onClick={() => handleLogout()}
                     className='h-10 flex items-center justify-center text-amber-700 font-medium cursor-pointer'>
                        <LogOut className="size-4 mr-2" />
                        Logout
                    </DropdownMenuItem>
                </div>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}

// import { Button } from "@/components/ui/button"
// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuGroup,
//   DropdownMenuItem,
//   DropdownMenuLabel,
//   DropdownMenuPortal,
//   DropdownMenuSeparator,
//   DropdownMenuShortcut,
//   DropdownMenuSub,
//   DropdownMenuSubContent,
//   DropdownMenuSubTrigger,
//   DropdownMenuTrigger,
// } from "@/components/ui/dropdown-menu"

// export function UserButton() {
//   return (
//     <DropdownMenu>
//       <DropdownMenuTrigger asChild>
//         <Button variant="outline">Open</Button>
//       </DropdownMenuTrigger>
//       <DropdownMenuContent className="w-56">
//         <DropdownMenuLabel>My Account</DropdownMenuLabel>
//         <DropdownMenuSeparator />
//         <DropdownMenuGroup>
//           <DropdownMenuItem>
//             Profile
//             <DropdownMenuShortcut>⇧⌘P</DropdownMenuShortcut>
//           </DropdownMenuItem>
//           <DropdownMenuItem>
//             Billing
//             <DropdownMenuShortcut>⌘B</DropdownMenuShortcut>
//           </DropdownMenuItem>
//           <DropdownMenuItem>
//             Settings
//             <DropdownMenuShortcut>⌘S</DropdownMenuShortcut>
//           </DropdownMenuItem>
//           <DropdownMenuItem>
//             Keyboard shortcuts
//             <DropdownMenuShortcut>⌘K</DropdownMenuShortcut>
//           </DropdownMenuItem>
//         </DropdownMenuGroup>
//         <DropdownMenuSeparator />
//         <DropdownMenuGroup>
//           <DropdownMenuItem>Team</DropdownMenuItem>
//           <DropdownMenuSub>
//             <DropdownMenuSubTrigger>Invite users</DropdownMenuSubTrigger>
//             <DropdownMenuPortal>
//               <DropdownMenuSubContent>
//                 <DropdownMenuItem>Email</DropdownMenuItem>
//                 <DropdownMenuItem>Message</DropdownMenuItem>
//                 <DropdownMenuSeparator />
//                 <DropdownMenuItem>More...</DropdownMenuItem>
//               </DropdownMenuSubContent>
//             </DropdownMenuPortal>
//           </DropdownMenuSub>
//           <DropdownMenuItem>
//             New Team
//             <DropdownMenuShortcut>⌘+T</DropdownMenuShortcut>
//           </DropdownMenuItem>
//         </DropdownMenuGroup>
//         <DropdownMenuSeparator />
//         <DropdownMenuItem>GitHub</DropdownMenuItem>
//         <DropdownMenuItem>Support</DropdownMenuItem>
//         <DropdownMenuItem disabled>API</DropdownMenuItem>
//         <DropdownMenuSeparator />
//         <DropdownMenuItem>
//           Log out
//           <DropdownMenuShortcut>⇧⌘Q</DropdownMenuShortcut>
//         </DropdownMenuItem>
//       </DropdownMenuContent>
//     </DropdownMenu>
//   )
// }