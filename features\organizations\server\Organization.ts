import <PERSON><PERSON><PERSON><PERSON> from "@/lib/endpoints";
import axios from "axios";
import { toast } from "sonner";
import { CreateOrganizationFormData } from "../schemas";

export class Organization {
  
  public static async create(formData: CreateOrganizationFormData) {
    try {
      const token = localStorage.getItem("izi_token");
      
      if (!token) {
        throw new Error("Authentication token not found");
      }

      const response = await axios.post(IziApi.createOrganization, formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.status === 200 || response.status === 201) {
        toast.success("Organization created successfully");
        return response.data.data; 
      }
    } catch (error: any) {
      console.error("Organization creation error:", error);
      throw error.response?.data || error.message; 
    }
  }

  public static async getAll() {
    try {
      const token = localStorage.getItem("izi_token");
      
      if (!token) {
        throw new Error("Authentication token not found");
      }

      const response = await axios.get(IziApi.getOrganizations, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200) {
        console.log(response.data.data);
        return response.data.data; 
      }
    } catch (error: any) {
      console.error("Get organizations error:", error);
      throw error.response?.data || error.message; 
    }
  }

  public static async getById(id: number) {
    try {
      const token = localStorage.getItem("izi_token");
      
      if (!token) {
        throw new Error("Authentication token not found");
      }

      const response = await axios.get(`${IziApi.api}/organizations/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200) {
        return response.data.data; 
      }
    } catch (error: any) {
      console.error("Get organization error:", error);
      throw error.response?.data || error.message; 
    }
  }

  public static async update(id: number, formData: Partial<CreateOrganizationFormData>) {
    try {
      const token = localStorage.getItem("izi_token");
      
      if (!token) {
        throw new Error("Authentication token not found");
      }

      const response = await axios.put(`${IziApi.api}/organizations/${id}`, formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.status === 200) {
        return response.data.data; 
      }
    } catch (error: any) {
      console.error("Organization update error:", error);
      throw error.response?.data || error.message; 
    }
  }

  public static async delete(id: number) {
    try {
      const token = localStorage.getItem("izi_token");
      
      if (!token) {
        throw new Error("Authentication token not found");
      }

      const response = await axios.delete(`${IziApi.api}/organizations/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200) {
        return response.data; 
      }
    } catch (error: any) {
      console.error("Organization delete error:", error);
      throw error.response?.data || error.message; 
    }
  }

  public static async select(id: number) {
    try {
      const token = localStorage.getItem("izi_token");
      
      if (!token) {
        throw new Error("Authentication token not found");
      }

      const response = await axios.get(IziApi.singleOrganization(id), {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.status === 200) {
        console.log(response.data.data);
        return response.data.data; 
      }
    } catch (error: any) {
      console.error("Organization select error:", error);
      throw error.response?.data || error.message; 
    }
  }
}
