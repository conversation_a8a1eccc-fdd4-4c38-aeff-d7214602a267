export interface Organization {
  id: number;
  name: string;
  phone: string;
  address: string;
  description: string;
  featured_image?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  members_count?: number;
  logo?: string;
}

export interface CreateOrganizationRequest {
  name: string;
  phone: string;
  address: string;
  description: string;
  featured_image?: string;
}

export interface CreateOrganizationResponse {
  data: Organization;
  message: string;
}

export interface GetOrganizationsResponse {
  data: Organization[];
  message: string;
}

export interface SelectOrganizationResponse {
  data: {
    organization: Organization;
    token?: string;
  };
  message: string;
}
