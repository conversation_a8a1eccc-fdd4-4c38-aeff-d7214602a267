"use client"

import { ChooseOrganizationCard } from "@/features/organizations/components/choose-organization-card";
import { Building2 } from "lucide-react";

const ChooseOrganizationPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-emerald-50 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl">
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-primary rounded-2xl mx-auto mb-6 flex items-center justify-center">
            <Building2 className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Choose Your Organization
          </h1>
          <p className="text-gray-600 text-lg">
            Select an organization to continue
          </p>
        </div>
        <ChooseOrganizationCard />
      </div>
    </div>
  )
}

export default ChooseOrganizationPage
