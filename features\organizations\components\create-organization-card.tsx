"use client"

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Building2, Phone, MapPin, FileText } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { createOrganizationSchema, CreateOrganizationFormData } from "../schemas";
import { useCreateOrganization } from "../api/use-create-org";

export const CreateOrganizationCard = () => {
  const createOrganization = useCreateOrganization();

  const form = useForm<CreateOrganizationFormData>({
    resolver: zodResolver(createOrganizationSchema),
    defaultValues: {
      name: "",
      phone: "",
      address: "",
      description: "",
    }
  });

  

  const onSubmit = (data: CreateOrganizationFormData) => {
    createOrganization.mutate(data);
  };

  return (
    <Card className="w-full border-none shadow-2xl bg-white/95 backdrop-blur-sm">
      <CardHeader className="text-center pb-2">
        <div className="w-20 h-20 bg-primary rounded-2xl mx-auto mb-6 flex items-center justify-center">
          <Building2 className="w-10 h-10 text-white" />
        </div>
        <CardTitle className="text-2xl md:text-3xl font-bold text-gray-900">
          Organization Details
        </CardTitle>
        {/* <p className="text-gray-600">
          Fill in the information below to create your organization
        </p> */}
      </CardHeader>
      
      <CardContent className="p-6 md:p-8">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Featured Image Upload
            <div className="space-y-4">
              <FormLabel className="text-gray-700 font-medium flex items-center gap-2">
                <Camera className="w-4 h-4" />
                Organization Logo (Optional)
              </FormLabel>
              
              <div className="flex flex-col items-center space-y-4">
                {imagePreview ? (
                  <div className="relative">
                    <img
                      src={imagePreview}
                      alt="Organization logo preview"
                      className="w-32 h-32 object-cover rounded-xl border-4 border-gray-200 shadow-lg"
                    />
                    <button
                      type="button"
                      onClick={removeImage}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                ) : (
                  <div
                    onClick={() => fileInputRef.current?.click()}
                    className="w-32 h-32 border-2 border-dashed border-gray-300 rounded-xl flex flex-col items-center justify-center cursor-pointer hover:border-blue-400 hover:bg-blue-50 transition-all duration-200"
                  >
                    <Upload className="w-8 h-8 text-gray-400 mb-2" />
                    <span className="text-sm text-gray-500 text-center px-2">
                      Click to upload
                    </span>
                  </div>
                )}
                
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
                
                <p className="text-xs text-gray-500 text-center">
                  Recommended: Square image, max 5MB
                </p>
              </div>
              
              {form.formState.errors.featured_image && (
                <p className="text-red-500 text-sm text-center">
                  {form.formState.errors.featured_image.message}
                </p>
              )}
            </div> */}

            {/* Organization Name */}
            <FormField
              name="name"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium flex items-center gap-2">
                    <Building2 className="w-4 h-4" />
                    Organization Name
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Enter your organization name"
                      className="h-12 border-gray-200 focus:border-blue-400 focus:ring-blue-400/20 bg-white"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Phone Number */}
            <FormField
              name="phone"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium flex items-center gap-2">
                    <Phone className="w-4 h-4" />
                    Phone Number
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="tel"
                      placeholder="Enter phone number"
                      className="h-12 border-gray-200 focus:border-blue-400 focus:ring-blue-400/20 bg-white"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Address */}
            <FormField
              name="address"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium flex items-center gap-2">
                    <MapPin className="w-4 h-4" />
                    Address
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Enter organization address"
                      className="h-12 border-gray-200 focus:border-blue-400 focus:ring-blue-400/20 bg-white"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description */}
            <FormField
              name="description"
              control={form.control}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium flex items-center gap-2">
                    <FileText className="w-4 h-4" />
                    Description
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Describe your organization..."
                      className="min-h-[80px] border-gray-200 focus:border-blue-400 focus:ring-blue-400/20 bg-white resize-none"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Submit Button */}
            <Button
              type="submit"
              disabled={createOrganization.isPending}
              className="w-full h-12 bg-primary hover:from-blue-600 hover:to-indigo-700 text-white font-medium transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              {createOrganization.isPending ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Creating Organization...
                </div>
              ) : (
                "Create Organization"
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
